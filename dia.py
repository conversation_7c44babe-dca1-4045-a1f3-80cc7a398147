import requests
import random
import string
import time
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed

# ==== 配置 ====
BASE_URL = 'https://www.diabrowser.com/invite/'
CHARSET = string.digits + string.ascii_uppercase
CHECK_COUNT = 1000000
SEED = 123456789
OUTPUT_FILE = 'valid_invites.txt'
THREADS = 30  # 并发线程数

def generate_random_code():
    return ''.join(random.choices(CHARSET, k=6))

def is_invite_valid(code):
    url = BASE_URL + code
    try:
        headers = {'User-Agent': 'Mozilla/5.0'}
        response = requests.get(url, headers=headers, timeout=10, allow_redirects=True)
        if response.history:
            return False
        soup = BeautifulSoup(response.text, 'html.parser')
        return 'out of invites' not in soup.get_text().lower()
    except Exception as e:
        print(f"⚠️ {code}: 请求失败 - {str(e)}")
        return False

def check_code(code):
    valid = is_invite_valid(code)
    return code if valid else None

def main():
    print(f"📌 使用随机种子: {SEED}")
    random.seed(SEED)
    codes = set()
    while len(codes) < CHECK_COUNT:
        codes.add(generate_random_code())

    valid_codes = []

    with ThreadPoolExecutor(max_workers=THREADS) as executor:
        futures = {executor.submit(check_code, code): code for code in codes}
        for i, future in enumerate(as_completed(futures), 1):
            code = futures[future]
            try:
                result = future.result()
                if result:
                    print(f"[{i}/{CHECK_COUNT}] {code} ✅ 有效")
                    valid_codes.append(result)
                else:
                    print(f"[{i}/{CHECK_COUNT}] {code} ❌ 无效")
            except Exception as e:
                print(f"[{i}/{CHECK_COUNT}] {code} ⚠️ 错误: {str(e)}")

    with open(OUTPUT_FILE, 'a') as outfile:
        for code in valid_codes:
            outfile.write(BASE_URL + code + '\n')

    print(f"\n🎉 检查完成，发现 {len(valid_codes)} 个有效邀请码，保存于 {OUTPUT_FILE}")

if __name__ == '__main__':
    main()