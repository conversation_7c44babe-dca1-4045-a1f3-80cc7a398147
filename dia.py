import requests
import random
import string
import time
import threading
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed

# ==== 配置 ====
BASE_URL = 'https://www.diabrowser.com/invite/'
CHARSET = string.digits + string.ascii_uppercase
SEED = 123456789
OUTPUT_FILE = 'valid_invites.txt'
THREADS = 80  # 并发线程数

# 全局计数器和锁
checked_count = 0
found_count = 0
count_lock = threading.Lock()

def generate_random_code():
    return ''.join(random.choices(CHARSET, k=6))

def is_invite_valid(code):
    url = BASE_URL + code
    try:
        headers = {'User-Agent': 'Mozilla/5.0'}
        response = requests.get(url, headers=headers, timeout=10, allow_redirects=True)
        if response.history:
            return False
        soup = BeautifulSoup(response.text, 'html.parser')
        return 'out of invites' not in soup.get_text().lower()
    except Exception as e:
        print(f"⚠️ {code}: 请求失败 - {str(e)}")
        return False

def save_valid_code(code):
    """立即保存有效的邀请码到文件"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(BASE_URL + code + '\n')
        f.flush()  # 强制刷新到磁盘

def check_code_worker():
    """工作线程函数，持续检查随机生成的邀请码"""
    global checked_count, found_count

    while True:
        code = generate_random_code()

        try:
            valid = is_invite_valid(code)

            with count_lock:
                checked_count += 1
                current_checked = checked_count
                current_found = found_count

                if valid:
                    found_count += 1
                    current_found = found_count
                    save_valid_code(code)
                    print(f"[{current_checked}] {code} ✅ 有效 (总计找到: {current_found})")
                else:
                    if current_checked % 100 == 0:  # 每100次检查显示一次进度
                        print(f"[{current_checked}] 已检查 {current_checked} 个，找到 {current_found} 个有效")

        except Exception as e:
            with count_lock:
                checked_count += 1
                current_checked = checked_count
            print(f"[{current_checked}] {code} ⚠️ 错误: {str(e)}")

        # 添加小延迟避免过于频繁的请求
        time.sleep(0.1)

def main():
    print(f"📌 使用随机种子: {SEED}")
    print(f"🚀 启动 {THREADS} 个线程持续检查邀请码...")
    print(f"💾 有效邀请码将立即保存到: {OUTPUT_FILE}")
    print("🔄 程序将持续运行，按 Ctrl+C 停止")
    print("-" * 50)

    random.seed(SEED)

    # 创建输出文件（如果不存在）
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        pass

    try:
        # 启动工作线程
        with ThreadPoolExecutor(max_workers=THREADS) as executor:
            # 提交所有工作线程
            futures = [executor.submit(check_code_worker) for _ in range(THREADS)]

            # 等待所有线程（实际上会一直运行）
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"线程异常: {e}")

    except KeyboardInterrupt:
        print(f"\n\n🛑 程序被用户中断")
        print(f"📊 总计检查: {checked_count} 个邀请码")
        print(f"✅ 找到有效: {found_count} 个邀请码")
        print(f"💾 结果已保存到: {OUTPUT_FILE}")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        print(f"📊 总计检查: {checked_count} 个邀请码")
        print(f"✅ 找到有效: {found_count} 个邀请码")

if __name__ == '__main__':
    main()