import requests
import time

# Base62 字符表（顺序非常关键）
base62_chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
BASE = 62
prefix = 'F' #修改第一位短链，目前为 F
output_file = 'vip_links.txt'       # vip-invite-cashier 链接保存位置
gift_file = 'gift_links.txt'        # gift-receive 链接保存位置

def base62_to_int(s):
    num = 0
    for c in s:
        num = num * BASE + base62_chars.index(c)
    return num

def int_to_base62(n, length=5):
    s = ''
    while n > 0:
        s = base62_chars[n % BASE] + s
        n //= BASE
    return s.rjust(length, '0')

def check_short_link(code):
    url = f"http://163cn.tv/{code}"
    try:
        resp = requests.head(url, allow_redirects=False, timeout=5)
        if resp.status_code in [301, 302] and 'Location' in resp.headers:
            location = resp.headers['Location']
            if 'vip-invite-cashier' in location:
                print(f"[✅ vip链接] {url} → {location}")
                with open(output_file, 'a', encoding='utf-8') as f:
                    f.write(url + '\n')
                return True
            elif 'gift-receive' in location:
                print(f"[🎁 礼物链接] {url} → {location}")
                with open(gift_file, 'a', encoding='utf-8') as f:
                    f.write(url + '\n')
                return True
            else:
                print(f"[⚠️ 跳转但不符] {url} → {location}")
        else:
            print(f"[❌ 无效] {url} → 状态码: {resp.status_code}")
    except Exception as e:
        print(f"[⚠️ 错误] {url} → {e}")
    return False

if __name__ == "__main__":
    start_suffix = "Bm5xqZ" # 这里写后6位短链
    start_id = base62_to_int(start_suffix)
    max_id = start_id + 900000000 #根据自己的需要，设置最大遍历范围

    sleep_every = 50

    i = start_id
    checked = 0

    while i < max_id:
        found = False
        for j in range(4):
            current_id = i + j
            suffix = int_to_base62(current_id)
            code = prefix + suffix
            print(f"{current_id:>10} / {max_id:<10} - 正在检查: {code}", end='\r')
            result = check_short_link(code)
            checked += 1
            if result:
                found = True

            if checked % sleep_every == 0:
                time.sleep(2)

        i += 344  # 跳过 86 * 4 个编码